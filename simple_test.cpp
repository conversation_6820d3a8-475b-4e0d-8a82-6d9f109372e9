#include "simple_test.h"
#include "simpleconfigmanager.h"
#include "simplefiledatasource.h"
#include "simplelog4qtdatasource.h"
#include <QApplication>
#include <QSplitter>
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>

SimpleTestWidget::SimpleTestWidget(QWidget* parent)
    : QWidget(parent)
    , m_fileViewer(nullptr)
    , m_log4qtViewer(nullptr)
{
    setupUI();
    connectSignals();
    
    // 设置窗口属性
    setWindowTitle("LogViewer 阶段1 测试程序");
    resize(1200, 800);
    
    // 显示配置信息
    showWelcomeMessage();

    m_testDate = new QTimer(this);
    connect(m_testDate, &QTimer::timeout, this, &SimpleTestWidget::generateTestData);
}

void SimpleTestWidget::setupUI()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // 创建分割器
    QSplitter* splitter = new QSplitter(Qt::Horizontal, this);
    
    // 左侧控制面板
    QWidget* controlPanel = new QWidget();
    controlPanel->setMaximumWidth(300);
    controlPanel->setMinimumWidth(250);
    
    QVBoxLayout* controlLayout = new QVBoxLayout(controlPanel);
    
    // 查看器创建按钮
    controlLayout->addWidget(new QLabel("查看器创建:"));
    m_createFileViewerBtn = new QPushButton("创建文件查看器");
    m_createLog4QtViewerBtn = new QPushButton("创建Log4Qt查看器");
    controlLayout->addWidget(m_createFileViewerBtn);
    controlLayout->addWidget(m_createLog4QtViewerBtn);
    
    controlLayout->addWidget(new QLabel(""));
    
    // 文件操作
    controlLayout->addWidget(new QLabel("文件操作:"));
    QHBoxLayout* encodingLayout = new QHBoxLayout();
    encodingLayout->addWidget(new QLabel("编码:"));
    m_encodingEdit = new QLineEdit("UTF-8");
    encodingLayout->addWidget(m_encodingEdit);
    controlLayout->addLayout(encodingLayout);
    
    m_browseBtn = new QPushButton("浏览文件");
    m_loadFileBtn = new QPushButton("加载文件");
    controlLayout->addWidget(m_browseBtn);
    controlLayout->addWidget(m_loadFileBtn);
    
    controlLayout->addWidget(new QLabel(""));
    
    // Log4Qt操作
    controlLayout->addWidget(new QLabel("Log4Qt操作:"));
    QHBoxLayout* loggerLayout = new QHBoxLayout();
    loggerLayout->addWidget(new QLabel("日志器:"));
    m_loggerNameEdit = new QLineEdit("TestLogger");
    loggerLayout->addWidget(m_loggerNameEdit);
    controlLayout->addLayout(loggerLayout);
    
    m_connectLog4QtBtn = new QPushButton("连接Log4Qt");
    m_generateTestBtn = new QPushButton("生成测试数据");
    m_stopgenerateTestBtn = new QPushButton("停止测试数据");
    controlLayout->addWidget(m_connectLog4QtBtn);
    controlLayout->addWidget(m_generateTestBtn);
    controlLayout->addWidget(m_stopgenerateTestBtn);
    
    controlLayout->addWidget(new QLabel(""));
    
    // 测试按钮
    controlLayout->addWidget(new QLabel("测试功能:"));
    m_testConfigBtn = new QPushButton("测试配置管理器");
    m_clearLogBtn = new QPushButton("清除日志");
    controlLayout->addWidget(m_testConfigBtn);
    controlLayout->addWidget(m_clearLogBtn);
    
    controlLayout->addStretch();
    
    // 日志显示区域
    m_logEdit = new QTextEdit();
    m_logEdit->setMaximumHeight(200);
    controlLayout->addWidget(new QLabel("测试日志:"));
    controlLayout->addWidget(m_logEdit);
    
    // 右侧查看器区域
    QWidget* viewerPanel = new QWidget();
    m_viewerLayout = new QVBoxLayout(viewerPanel);
    m_viewerLayout->addWidget(new QLabel("查看器显示区域 (请先创建查看器)"));
    
    m_viewerContainer = viewerPanel;
    
    // 添加到分割器
    splitter->addWidget(controlPanel);
    splitter->addWidget(viewerPanel);
    splitter->setStretchFactor(0, 0);
    splitter->setStretchFactor(1, 1);
    
    mainLayout->addWidget(splitter);
}

void SimpleTestWidget::connectSignals()
{
    connect(m_createFileViewerBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::createFileViewer);
    connect(m_createLog4QtViewerBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::createLog4QtViewer);
    connect(m_browseBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::browseFile);
    connect(m_loadFileBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::loadFile);
    connect(m_connectLog4QtBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::connectLog4Qt);
    connect(m_generateTestBtn, &QPushButton::clicked,
            this, [this](){m_testDate->start();});

    connect(m_stopgenerateTestBtn, &QPushButton::clicked,
            this, [this](){m_testDate->stop();});

    connect(m_testConfigBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::testConfig);
    connect(m_clearLogBtn, &QPushButton::clicked,
            this, &SimpleTestWidget::clearDisplay);
}

void SimpleTestWidget::showWelcomeMessage()
{
    m_logEdit->append("=== LogViewer 阶段1 测试程序 ===");
    m_logEdit->append("测试目标:");
    m_logEdit->append("1. SimpleLogViewer UI显示");
    m_logEdit->append("2. 文件查看器基础功能");
    m_logEdit->append("3. Log4Qt查看器基础功能");
    m_logEdit->append("4. 配置管理器功能");
    m_logEdit->append("5. 数据源连接功能");
    m_logEdit->append("");
}

void SimpleTestWidget::createFileViewer()
{
    if (m_fileViewer) {
        delete m_fileViewer;
    }
    
    m_fileViewer = new SimpleLogViewer(SimpleLogViewer::FileViewer, this);
    m_viewerLayout->addWidget(m_fileViewer);
    
    // 连接信号
    connect(m_fileViewer, &SimpleLogViewer::dataLoaded,
            this, &SimpleTestWidget::onDataLoaded);
    connect(m_fileViewer, &SimpleLogViewer::errorOccurred,
            this, &SimpleTestWidget::onError);
    
    m_logEdit->append("✓ 文件查看器创建成功");
    qDebug() << "File viewer created";
}

void SimpleTestWidget::createLog4QtViewer()
{
    if (m_log4qtViewer) {
        delete m_log4qtViewer;
    }
    
    m_log4qtViewer = new SimpleLogViewer(SimpleLogViewer::Log4QtViewer, this);
    m_viewerLayout->addWidget(m_log4qtViewer);
    
    // 连接信号
    connect(m_log4qtViewer, &SimpleLogViewer::dataLoaded,
            this, &SimpleTestWidget::onDataLoaded);
    connect(m_log4qtViewer, &SimpleLogViewer::errorOccurred,
            this, &SimpleTestWidget::onError);
    
    m_logEdit->append("✓ Log4Qt查看器创建成功");
    qDebug() << "Log4Qt viewer created";
}

void SimpleTestWidget::browseFile()
{
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择日志文件",
        "",
        "日志文件 (*.log *.txt);;所有文件 (*.*)"
    );
    
    if (!fileName.isEmpty()) {
        m_logEdit->append(QString("选择文件: %1").arg(fileName));
        // 这里可以将文件路径存储到成员变量中
    }
}

void SimpleTestWidget::loadFile()
{
    if (!m_fileViewer) {
        QMessageBox::warning(this, "警告", "请先创建文件查看器");
        return;
    }
    
    QString fileName = QFileDialog::getOpenFileName(
        this,
        "选择日志文件",
        "",
        "日志文件 (*.log *.txt);;所有文件 (*.*)"
    );
    
    if (!fileName.isEmpty()) {
        m_logEdit->append(QString("正在加载文件: %1").arg(fileName));
        
        QString encoding = m_encodingEdit->text().trimmed();
        if (encoding.isEmpty()) {
            encoding = "UTF-8";
        }
        
        bool success = m_fileViewer->loadFile(fileName, encoding);
        if (success) {
            m_logEdit->append("✓ 文件加载请求已发送");
        } else {
            m_logEdit->append("✗ 文件加载失败");
        }
    }
}

void SimpleTestWidget::connectLog4Qt()
{
    if (!m_log4qtViewer) {
        QMessageBox::warning(this, "警告", "请先创建Log4Qt查看器");
        return;
    }
    
    QString loggerName = m_loggerNameEdit->text().trimmed();
    if (loggerName.isEmpty()) {
        loggerName = "TestLogger";
    }
    
    m_logEdit->append(QString("正在连接Log4Qt日志器: %1").arg(loggerName));
    
    bool success = m_log4qtViewer->connectLog4Qt(loggerName);
    if (success) {
        m_logEdit->append("✓ Log4Qt连接成功");
    } else {
        m_logEdit->append("✗ Log4Qt连接失败");
    }
}

void SimpleTestWidget::generateTestData()
{
    if (!m_log4qtViewer) {
        QMessageBox::warning(this, "警告", "请先创建Log4Qt查看器并连接");
        return;
    }
    
    m_logEdit->append("正在生成测试数据...");
    m_logEdit->append("✓ 测试数据生成功能已触发");

    qDebug() << "正在生成测试数据...";
    qDebug() << "正在生成测试数据...";
    qDebug() << "✓ 测试数据生成功能已触发";

    qInfo() << "正在生成测试数据...";
    qInfo() << "正在生成测试数据...";
    qInfo() << "✓ 测试数据生成功能已触发";

    qWarning() << "正在生成测试数据...";
    qWarning() << "正在生成测试数据...";
    qWarning() << "✓ 测试数据生成功能已触发";

    qCritical() << "正在生成测试数据...";
    qCritical() << "正在生成测试数据...";
    qCritical() << "✓ 测试数据生成功能已触发";

//    qFatal() << "正在生成测试数据...";
//    qFatal() << "正在生成测试数据...";
//    qFatal() << "✓ 测试数据生成功能已触发";
}

void SimpleTestWidget::testConfig()
{
    SimpleConfigManager& config = SimpleConfigManager::instance();
    
    m_logEdit->append("=== 配置管理器测试 ===");
    m_logEdit->append(QString("默认编码: %1").arg(config.getDefaultEncoding()));
    m_logEdit->append(QString("最大文件大小: %1 MB").arg(config.getMaxFileSize()));
    m_logEdit->append(QString("最大日志条目: %1").arg(config.getMaxLogEntries()));
    m_logEdit->append(QString("自动滚动: %1").arg(config.getAutoScroll() ? "是" : "否"));
    m_logEdit->append(QString("配置文件: %1").arg(config.getConfigFilePath()));
    
    // 测试配置修改
    config.setDefaultEncoding("GBK");
    config.setMaxFileSize(200);
    config.save();
    
    m_logEdit->append("✓ 配置测试完成");
}

void SimpleTestWidget::clearDisplay()
{
    m_logEdit->clear();
    showWelcomeMessage();
}

void SimpleTestWidget::onDataLoaded(int count)
{
    m_logEdit->append(QString("✓ 数据加载完成: %1 条日志").arg(count));
}

void SimpleTestWidget::onError(const QString& error)
{
    m_logEdit->append(QString("✗ 错误: %1").arg(error));
}

void SimpleTestWidget::clearViewerContainer()
{
    // 清除现有的查看器
    QLayoutItem* item;
    while ((item = m_viewerLayout->takeAt(0)) != nullptr) {
        if (item->widget()) {
            item->widget()->deleteLater();
        }
        delete item;
    }
    
    m_fileViewer = nullptr;
    m_log4qtViewer = nullptr;
}
