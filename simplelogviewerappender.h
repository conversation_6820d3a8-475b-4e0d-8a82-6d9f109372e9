#ifndef SIMPLELOGVIEWERAPPENDER_H
#define SIMPLELOGVIEWERAPPENDER_H

#include "logviewer_global.h"
#include "logentry.h"
#include <QObject>

// 条件包含Log4Qt相关头文件
#ifdef LOG4QT_AVAILABLE
#include <log4qt/appenderskeleton.h>
#include <log4qt/loggingevent.h>
#endif

/**
 * @brief 简化的LogViewer Appender
 *
 * 这是一个简化版本的Appender，用于替代复杂的LogViewerAppender。
 * 特点：
 * - 在LOG4QT_AVAILABLE时继承自Log4Qt::AppenderSkeleton，接收真实日志
 * - 在没有Log4Qt时作为普通QObject，支持测试数据
 * - 简化的信号机制
 * - 支持测试数据生成
 * - 线程安全的日志接收
 */
#ifdef LOG4QT_AVAILABLE
class LOGVIEWER_EXPORT SimpleLogViewerAppender : public Log4Qt::AppenderSkeleton
#else
class LOGVIEWER_EXPORT SimpleLogViewerAppender : public QObject
#endif
{
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SimpleLogViewerAppender(QObject* parent = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~SimpleLogViewerAppender() override;

    /**
     * @brief 模拟接收日志条目
     * @param entry 日志条目
     */
    void appendLogEntry(const LogEntry& entry);

    /**
     * @brief 生成测试日志条目
     * @param count 生成数量
     */
    void generateTestEntries(int count = 10);

    /**
     * @brief 设置是否启用
     * @param enabled 是否启用
     */
    void setEnabled(bool enabled);

    /**
     * @brief 检查是否启用
     * @return 启用状态
     */
    bool isEnabled() const { return m_enabled; }

    /**
     * @brief 获取接收到的日志条目总数
     * @return 总数
     */
    int getTotalEntryCount() const { return m_totalEntryCount; }

#ifdef LOG4QT_AVAILABLE
    // ========== Log4Qt Appender 接口实现 ==========

    /**
     * @brief Log4Qt日志事件处理方法
     * @param event Log4Qt日志事件
     */
    void append(const Log4Qt::LoggingEvent& event) override;

    /**
     * @brief 检查是否需要布局
     * @return 总是返回false，因为我们直接处理LoggingEvent
     */
    bool requiresLayout() const override;
#endif

signals:
    /**
     * @brief 新日志条目信号
     * @param entry 日志条目
     */
    void newLogEntry(const LogEntry& entry);

private:
    /**
     * @brief 创建测试日志条目
     * @param index 索引
     * @return 日志条目
     */
    LogEntry createTestEntry(int index) const;

#ifdef LOG4QT_AVAILABLE
    /**
     * @brief 将Log4Qt事件转换为LogEntry
     * @param event Log4Qt日志事件
     * @return 转换后的LogEntry
     */
    LogEntry convertToLogEntry(const Log4Qt::LoggingEvent& event) const;

    /**
     * @brief 将LogEntry级别转换为字符串
     * @param level 日志级别
     * @return 级别字符串
     */
    QString logLevelToString(LogEntry::LogLevel level) const;

    /**
     * @brief 获取相对时间字符串
     * @param timestamp 时间戳
     * @return 相对时间描述
     */
    QString getRelativeTimeString(const QDateTime& timestamp) const;
#endif

private:
    bool m_enabled;             ///< 是否启用
    int m_totalEntryCount;      ///< 总接收条目数
};

#endif // SIMPLELOGVIEWERAPPENDER_H
