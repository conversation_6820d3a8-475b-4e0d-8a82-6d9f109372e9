#include "logmodel.h"
#include "simpleconfigmanager.h"
#include <QColor>
#include <QBrush>
#include <QFont>
#include <QtGlobal>
#include <QDebug>

LogModel::LogModel(QObject* parent) : QAbstractTableModel(parent), _entries(10000)
{
    // 从简化配置管理器获取最大条目数
    SimpleConfigManager& config = SimpleConfigManager::instance();
    _maxEntries = static_cast<uint64_t>(config.getMaxLogEntries());

    // 设置环形缓冲区容量
    _entries.setCapacity(static_cast<int>(_maxEntries));

    _columnVisibility.resize(static_cast< int >(ColumnCount));
    for (int i = 0; i < static_cast< int >(ColumnCount); ++i)
    {
        _columnVisibility[i] = true; // 默认所有列可见
    }

    // 初始化UI更新优化相关变量
    _uiUpdateTimer = new QTimer(this);
    _uiUpdateTimer->setSingleShot(true);
    _uiUpdateTimer->setInterval(100); // 100ms延迟，减少高频更新
    connect(_uiUpdateTimer, &QTimer::timeout, this, &LogModel::performScheduledUIUpdate);

    _pendingUIUpdate = false;
    _lastUpdateSize = 0;

    // 简化版本不需要配置变化信号连接
    qDebug() << "LogModel initialized with max entries:" << _maxEntries;
}

LogModel::~LogModel() {}

int LogModel::rowCount(const QModelIndex& parent) const
{
    if (parent.isValid())
        return 0;
    QMutexLocker locker(&_mutex);
    return _entries.size();
}

int LogModel::columnCount(const QModelIndex& parent) const
{
    if (parent.isValid())
        return 0;

    // 这里不需要锁，因为列数是固定的
    int visibleCount = 0;
    for (int i = 0; i < static_cast< int >(ColumnCount); ++i)
    {
        if (_columnVisibility[i])
            ++visibleCount;
    }
    return visibleCount;
}

QVariant LogModel::data(const QModelIndex& index, int role) const
{
    if (!index.isValid())
        return QVariant();

    LogEntry entry;
    int      actualColumn = -1;

    {
        QMutexLocker locker(&_mutex);
        if (index.row() >= _entries.size() || index.row() < 0)
            return QVariant();

        entry = _entries.at(index.row()); // 使用环形缓冲区的at方法

        // 计算实际列号
        int visibleCount = 0;
        for (int i = 0; i < static_cast< int >(ColumnCount); ++i)
        {
            if (_columnVisibility[i])
            {
                if (visibleCount == index.column())
                {
                    actualColumn = i;
                    break;
                }
                visibleCount++;
            }
        }
    } // 锁在这里释放

    if (actualColumn < 0 || actualColumn >= static_cast< int >(ColumnCount))
        return QVariant();

    // 处理不同的角色
    switch (role)
    {
        case Qt::DisplayRole:
            switch (actualColumn)
            {
                case TimestampColumn:
                    return entry.timestamp().toString("yyyy-MM-dd hh:mm:ss.zzz");
                case LevelColumn:
                    return entry.levelString();
                case SourceColumn:
                    return entry.source();
                case MessageColumn:
                    return entry.message();
                case DetailsColumn:
                    return entry.details();
                default:
                    return QVariant();
            }

        case Qt::BackgroundRole:
            switch (entry.level())
            {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(240, 240, 240));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(255, 255, 255));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(255, 255, 200));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(255, 200, 200));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(255, 150, 150));
                default:
                    return QVariant();
            }

        case Qt::ForegroundRole:
            switch (entry.level())
            {
                case LogEntry::LogLevel::Debug:
                    return QBrush(QColor(100, 100, 100));
                case LogEntry::LogLevel::Info:
                    return QBrush(QColor(0, 0, 0));
                case LogEntry::LogLevel::Warning:
                    return QBrush(QColor(150, 100, 0));
                case LogEntry::LogLevel::Error:
                    return QBrush(QColor(200, 0, 0));
                case LogEntry::LogLevel::Critical:
                    return QBrush(QColor(150, 0, 0));
                default:
                    return QVariant();
            }

        case Qt::FontRole: {
            QFont font;
            if (entry.level() == LogEntry::LogLevel::Critical || entry.level() == LogEntry::LogLevel::Error)
            {
                font.setBold(true);
            }
            return font;
        }

        case Qt::TextAlignmentRole:
            switch (actualColumn)
            {
                case TimestampColumn:
                case LevelColumn:
                    return Qt::AlignCenter;
                default:
                    return QVariant(Qt::AlignLeft | Qt::AlignVCenter);
            }

        default:
            return QVariant();
    }
}

QVariant LogModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation != Qt::Horizontal)
        return QVariant();

    // 计算实际列号，避免在锁内调用 tr()
    int actualColumn;
    {
        QMutexLocker locker(&_mutex);
        actualColumn = mapToActualColumn(section);
    }

    if (actualColumn == -1)
        return QVariant();

    if (role == Qt::DisplayRole)
    {
        switch (actualColumn)
        {
            case TimestampColumn:
                return tr("时间戳");
            case LevelColumn:
                return tr("级别");
            case SourceColumn:
                return tr("来源");
            case MessageColumn:
                return tr("消息");
            case DetailsColumn:
                return tr("详情");
            default:
                return QVariant();
        }
    }
    else if (role == Qt::SizeHintRole)
    {
        return QSize(100, -1);
    }

    return QVariant();
}

void LogModel::addLogEntry(const LogEntry& entry)
{
    try {
        QMutexLocker locker(&_mutex);

        int currentSize = _entries.size();
        bool isFull = _entries.isFull();

        // 释放锁进行UI操作
        locker.unlock();

        // 如果缓冲区已满，环形缓冲区会自动覆盖最旧的数据
        // 我们需要通知视图数据变化
        if (isFull) {
            // 缓冲区已满，新数据会覆盖最旧的数据
            // 通知视图整行数据都可能发生变化
            beginResetModel();
            locker.relock();
            _entries.append(entry);
            locker.unlock();
            endResetModel();
        } else {
            // 缓冲区未满，正常添加
            beginInsertRows(QModelIndex(), currentSize, currentSize);
            locker.relock();
            _entries.append(entry);
            locker.unlock();
            endInsertRows();
        }
    }
    catch (const std::exception& e) {
        qWarning() << "添加日志条目时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "添加日志条目时发生未知异常";
    }
}

void LogModel::addLogEntries(const QVector< LogEntry >& entries)
{
    qDebug() << "LogModel::addLogEntries: Called with" << entries.size() << "entries";

    if (entries.isEmpty()) {
        qDebug() << "LogModel::addLogEntries: Entries is empty, returning";
        return;
    }

    qDebug() << "LogModel::addLogEntries: Starting to process entries";
    try {
        // 检查是否需要内存清理
        if (_entries.needsMemoryCleanup()) {
            qDebug() << "LogModel::addLogEntries: 检测到内存压力，执行清理";
            _entries.forceMemoryCleanup();
            emit memoryWarning();
        }

        // 使用环形缓冲区，简化逻辑
        QMutexLocker locker(&_mutex);
        int currentSize = _entries.size();
        bool willOverflow = (currentSize + entries.size()) > _entries.capacity();
        locker.unlock();

        // 对于高频数据，使用延迟UI更新策略
        if (entries.size() > 100 || willOverflow) {
            // 高频数据场景，使用延迟更新
            qDebug() << "LogModel::addLogEntries: 高频数据，使用延迟UI更新";
            locker.relock();
            _entries.append(entries);
            locker.unlock();

            // 安排延迟UI更新
            scheduleUIUpdate();
        } else {
            // 低频数据，正常更新
            if (willOverflow) {
                beginResetModel();
                locker.relock();
                _entries.append(entries);
                locker.unlock();
                endResetModel();
            } else {
                int lastRow = currentSize + entries.size() - 1;
                beginInsertRows(QModelIndex(), currentSize, lastRow);
                locker.relock();
                _entries.append(entries);
                locker.unlock();
                endInsertRows();
            }
        }
    } 
    catch (const std::exception& e) {
        qWarning() << "处理日志条目时发生异常:" << e.what();
    } 
    catch (...) {
        qWarning() << "处理日志条目时发生未知异常";
    }
}

LogEntry LogModel::getLogEntry(int row) const
{
    QMutexLocker locker(&_mutex);
    return (row >= 0 && row < _entries.size()) ? _entries.at(row) : LogEntry();
}

QVector< LogEntry > LogModel::getEntries(int startIndex, int count) const
{
    QMutexLocker locker(&_mutex);
    if (startIndex < 0 || startIndex >= _entries.size() || count <= 0)
        return QVector< LogEntry >();
    int actualCount = qMin(count, _entries.size() - startIndex);
    return _entries.mid(startIndex, actualCount);
}

void LogModel::clear()
{
    try {
        beginResetModel();
        {
            QMutexLocker locker(&_mutex);
            _entries.clear();
            _entries.squeeze(); // 释放未使用的内存空间
        }
        endResetModel();
    } 
    catch (const std::exception& e) {
        qWarning() << "清除日志时发生异常:" << e.what();
    }
    catch (...) {
        qWarning() << "清除日志时发生未知异常";
    }
}

void LogModel::setMaxEntries(uint64_t maxEntries)
{
    QMutexLocker locker(&_mutex);
    _maxEntries = maxEntries > 0 ? maxEntries : 0;

    // 设置环形缓冲区的新容量
    if (_maxEntries > 0) {
        _entries.setCapacity(static_cast<int>(_maxEntries));
    }

    qDebug() << "LogModel max entries set to:" << _maxEntries;
}

int LogModel::getTotalCount() const
{
    QMutexLocker locker(&_mutex);
    return _entries.size();
}

void LogModel::setColumnVisible(Column column, bool visible)
{
    if (column < 0 || column >= ColumnCount)
        return;
    QMutexLocker locker(&_mutex);
    if (_columnVisibility[column] != visible)
    {
        beginResetModel();
        _columnVisibility[column] = visible;
        endResetModel();
    }
}

bool LogModel::isColumnVisible(Column column) const
{
    QMutexLocker locker(&_mutex);
    return (column >= 0 && column < ColumnCount) ? _columnVisibility[column] : false;
}

int LogModel::mapToActualColumn(int visibleColumn) const
{
    if (visibleColumn < 0)
        return -1;
    int visibleCount = 0;
    for (int i = 0; i < static_cast< int >(ColumnCount); ++i)
    {
        if (_columnVisibility[i])
        {
            if (visibleCount == visibleColumn)
                return i;
            visibleCount++;
        }
    }
    return -1;
}

void LogModel::removeEntriesAt(int startIndex, int count)
{
    // 环形缓冲区不支持任意位置删除，使用重置模型的方式
    if (count <= 0 || startIndex < 0) {
        return;
    }

    QMutexLocker locker(&_mutex);

    if (startIndex >= _entries.size()) {
        return;
    }

    // 获取当前所有数据
    QVector<LogEntry> currentEntries = _entries.toVector();

    // 计算实际删除数量
    int actualCount = qMin(count, currentEntries.size() - startIndex);

    // 创建新的数据集（删除指定范围）
    QVector<LogEntry> newEntries;
    newEntries.reserve(currentEntries.size() - actualCount);

    // 添加删除范围之前的数据
    for (int i = 0; i < startIndex; ++i) {
        newEntries.append(currentEntries[i]);
    }

    // 添加删除范围之后的数据
    for (int i = startIndex + actualCount; i < currentEntries.size(); ++i) {
        newEntries.append(currentEntries[i]);
    }

    locker.unlock();

    // 重置模型
    beginResetModel();
    locker.relock();
    _entries.clear();
    _entries.append(newEntries);
    locker.unlock();
    endResetModel();

    qDebug() << "Removed" << actualCount << "entries starting from index" << startIndex;
}

void LogModel::retainDataRange(int startRow, int endRow, int marginCount)
{
    QMutexLocker lock(&_mutex);

        if (_entries.isEmpty() || startRow >= _entries.size())
            return;

        // 确保有效范围
        startRow = qMax(0, startRow);
        endRow = qMin(endRow, _entries.size() - 1);
        marginCount = qMax(0, marginCount);

        // 创建新向量存放保留的条目
        QVector<LogEntry> retainedEntries;

        // 预分配空间以避免重新分配
        int estimatedSize = (endRow - startRow + 1) + qMin(marginCount * 2, _entries.size());
        retainedEntries.reserve(estimatedSize);

        // 获取当前所有数据
        QVector<LogEntry> currentEntries = _entries.toVector();

        // 保留开头的部分条目
        int headMargin = qMin(marginCount, startRow);
        for (int i = 0; i < headMargin; ++i)
        {
            retainedEntries.append(currentEntries[i]);
        }

        // 保留可见范围及缓冲区内的条目
        for (int i = startRow; i <= endRow && i < currentEntries.size(); ++i)
        {
            retainedEntries.append(currentEntries[i]);
        }

        // 保留末尾的部分条目
        int tailStart = qMax(endRow + 1, currentEntries.size() - marginCount);
        for (int i = tailStart; i < currentEntries.size(); ++i)
        {
            retainedEntries.append(currentEntries[i]);
        }

        lock.unlock();

        // 开始模型重置
        beginResetModel();
        lock.relock();

        // 替换旧条目
        _entries.clear();
        _entries.append(retainedEntries);
        _maxEntries = _entries.size();

        lock.unlock();

        // 结束模型重置
        endResetModel();

        qDebug() << QString("数据清理完成: 从 %1 条减少到 %2 条").arg(_entries.size() + (endRow - startRow + 1) - retainedEntries.size()).arg(_entries.size());
}

// doRemoveEntries方法已被移除，因为环形缓冲区不支持任意位置删除
// 相关功能已在removeEntriesAt中重新实现

void LogModel::scheduleUIUpdate()
{
    if (!_pendingUIUpdate) {
        _pendingUIUpdate = true;
        _uiUpdateTimer->start();
    }
}

void LogModel::performScheduledUIUpdate()
{
    if (!_pendingUIUpdate) {
        return;
    }

    _pendingUIUpdate = false;

    QMutexLocker locker(&_mutex);
    int currentSize = _entries.size();
    locker.unlock();

    // 检查数据是否有变化
    if (currentSize != _lastUpdateSize) {
        qDebug() << "LogModel::performScheduledUIUpdate: 执行延迟UI更新，数据大小:" << currentSize;

        // 使用resetModel进行批量更新，避免复杂的增量更新逻辑
        beginResetModel();
        endResetModel();

        _lastUpdateSize = currentSize;
    }
}
